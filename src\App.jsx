import React, { useState, useEffect } from 'react';
import { authAPI, jobsAPI, applicationsAPI, usersAPI } from './services/api';
import AdvancedJobSearch from './components/AdvancedJobSearch';
import EnhancedJobCard from './components/EnhancedJobCard';
import AssessmentSystem from './components/AssessmentSystem';
import SmartAutomation from './components/SmartAutomation';
import InterviewWorkflow from './components/InterviewWorkflow';
import AnalyticsInsights from './components/AnalyticsInsights';
import { filterJobs, sortJobsByRelevance, getFilterSummary } from './utils/jobFilters';
import './enhanced-styles.css';

export default function App() {
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Job Portal Dashboard</h1>
      <AdvancedJobSearch />
      <div className="my-6">
        <EnhancedJobCard />
      </div>
      <AssessmentSystem />
      <SmartAutomation />
      <InterviewWorkflow />
      <AnalyticsInsights />
    </div>
  );
}