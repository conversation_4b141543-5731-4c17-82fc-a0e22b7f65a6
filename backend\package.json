{"name": "job-portal-backend", "version": "1.0.0", "description": "Backend API for Job Portal application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.1.0"}}